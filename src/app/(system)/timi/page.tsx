import RefreshButton from '@/components/refresh-button';
import TimiP<PERSON> from '@/components/timi-play';
import <PERSON>iRunList from '@/components/timi-run-list';
import TimiRunning from '@/components/timi-running';
import TimiStatus from '@/components/timi-status';
import { createClient } from '@/services/supabase';
import { trivagoServicePing } from '@/services/timi';

const Timi = async () => {
  const supabase = await createClient();

  // Existing queries
  const { data: runs } = await supabase
    .from('run')
    .select('*')
    .not('ended_at', 'is', null)
    .order('created_at', { ascending: false });

  const { data: running } = await supabase
    .from('run')
    .select('*')
    .is('ended_at', null)
    .single();

  const { data: trivagoStatus } = await trivagoServicePing();

  // Dashboard data queries
  const [
    { count: totalRuns },
    { count: successfulChanges },
    { count: failedChanges },
    { count: totalChanges },
    { data: avgHotelsData },
    { data: totalHotelsData },
    { count: runsThisMonth }
  ] = await Promise.all([
    // Total runs count
    supabase.from('run').select('*', { count: 'exact', head: true }),

    // Successful changes count
    supabase
      .from('run_hotel_change')
      .select('*', { count: 'exact', head: true })
      .eq('success', true),

    // Failed changes count
    supabase
      .from('run_hotel_change')
      .select('*', { count: 'exact', head: true })
      .eq('success', false),

    // Total changes count
    supabase
      .from('run_hotel_change')
      .select('*', { count: 'exact', head: true }),

    // Average hotels per run
    supabase
      .from('run')
      .select('hotels_count.avg()')
      .not('hotels_count', 'is', null),

    // Total hotels processed
    supabase
      .from('run')
      .select('hotels_count.sum()')
      .not('hotels_count', 'is', null),

    // Runs this month
    supabase
      .from('run')
      .select('*', { count: 'exact', head: true })
      .gte(
        'created_at',
        new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          1
        ).toISOString()
      )
  ]);

  // Calculate success rate
  const successRate =
    totalChanges && totalChanges > 0
      ? (((successfulChanges || 0) / totalChanges) * 100).toFixed(1)
      : '0';

  const avgHotels = avgHotelsData?.[0]?.avg
    ? Math.round(avgHotelsData[0].avg)
    : 0;
  const totalHotels = totalHotelsData?.[0]?.sum || 0;

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Timi</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex gap-6'>
        <div className='w-full flex flex-col gap-3'>
          <div className='grid grid-cols-4 gap-4'>
            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>Total Runs</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {totalRuns || 0}
                  </p>
                </div>
                <div className='w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-blue-600 dark:text-blue-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'></path>
                  </svg>
                </div>
              </div>
            </div>

            
            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>Success Rate</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {successRate}%
                  </p>
                </div>
                <div className='w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-purple-600 dark:text-purple-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path d='M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z'></path>
                    <path d='M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z'></path>
                  </svg>
                </div>
              </div>
            </div>

            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>This Month</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {runsThisMonth || 0}
                  </p>
                </div>
                <div className='w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-orange-600 dark:text-orange-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path
                      fillRule='evenodd'
                      d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z'
                      clipRule='evenodd'
                    ></path>
                  </svg>
                </div>
              </div>
            </div>

            {/* Additional Stats Row */}
            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>Total Hotels</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {totalHotels.toLocaleString()}
                  </p>
                </div>
                <div className='w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-indigo-600 dark:text-indigo-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path d='M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35.524 9.015 9.015 0 00-.4.04z'></path>
                  </svg>
                </div>
              </div>
            </div>

            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>Avg Hotels/Run</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {avgHotels.toLocaleString()}
                  </p>
                </div>
                <div className='w-8 h-8 bg-teal-100 dark:bg-teal-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-teal-600 dark:text-teal-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path d='M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z'></path>
                  </svg>
                </div>
              </div>
            </div>

            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>Successful Changes</p>
                  <p className='text-2xl font-bold text-green-600 dark:text-green-400'>
                    {successfulChanges || 0}
                  </p>
                </div>
                <div className='w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-green-600 dark:text-green-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path
                      fillRule='evenodd'
                      d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                      clipRule='evenodd'
                    ></path>
                  </svg>
                </div>
              </div>
            </div>

            <div className='bg-white rounded-default p-3'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-500'>Failed Changes</p>
                  <p className='text-2xl font-bold text-red-600 dark:text-red-400'>
                    {failedChanges || 0}
                  </p>
                </div>
                <div className='w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center'>
                  <svg
                    className='w-4 h-4 text-red-600 dark:text-red-400'
                    fill='currentColor'
                    viewBox='0 0 20 20'
                  >
                    <path
                      fillRule='evenodd'
                      d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
                      clipRule='evenodd'
                    ></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <TimiRunList runs={runs ?? []} />
        </div>
        <div className='w-[350px] flex flex-col gap-3 flex-none'>
          <TimiStatus trivagoStatus={trivagoStatus} />
          {running && <TimiRunning runId={running!.id} />}
          {!running && <TimiPlay trivagoStatus={trivagoStatus} />}
        </div>
      </div>
    </div>
  );
};

export default Timi;
