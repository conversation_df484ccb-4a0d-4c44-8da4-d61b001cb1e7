'use client';

const TimiStatus = ({ trivagoStatus }: { trivagoStatus: boolean }) => {
  return (
    <div className='w-full flex flex-col gap-2 bg-white rounded-default p-3'>
      <p className='text-sm text-gray-500'>Status</p>
      <div className='flex items-center bg-success-100/50 px-4 py-2 rounded-inner'>
        <span className='relative flex size-3'>
          <span
            className={`absolute inline-flex h-full w-full animate-ping rounded-full ${trivagoStatus ? 'bg-success-500' : 'bg-red-500'} opacity-75`}
          ></span>
          <span
            className={`relative inline-flex size-3 rounded-full ${trivagoStatus ? 'bg-success-500' : 'bg-red-500'}`}
          ></span>
        </span>
        <div className='flex flex-col gap-0.5 ml-4'>
          <p className='text-gray-900 font-medium text-sm leading-4'>
            Trivago Service
          </p>
          <p
            className={`text-xs ${trivagoStatus ? 'text-success-800' : 'text-red-800'} leading-3`}
          >
            {trivagoStatus ? 'Online' : 'Offline'}
          </p>
        </div>
      </div>
      <div className='flex items-center bg-success-100/50 px-4 py-2 rounded-inner'>
        <span className='relative flex size-3'>
          <span className='absolute inline-flex h-full w-full animate-ping rounded-full bg-success-500 opacity-75'></span>
          <span className='relative inline-flex size-3 rounded-full bg-success-500'></span>
        </span>
        <div className='flex flex-col gap-0.5 ml-4'>
          <p className='text-gray-900 font-medium text-sm leading-4'>
            Intelligence Service
          </p>
          <p className='text-xs text-success-800 leading-3'>Há 3 minutos</p>
        </div>
      </div>
      <div className='flex items-center bg-success-100/50 px-4 py-2 rounded-inner'>
        <span className='relative flex size-3'>
          <span className='absolute inline-flex h-full w-full animate-ping rounded-full bg-success-500 opacity-75'></span>
          <span className='relative inline-flex size-3 rounded-full bg-success-500'></span>
        </span>
        <div className='flex flex-col gap-0.5 ml-4'>
          <p className='text-gray-900 font-medium text-sm leading-4'>
            Timi Service
          </p>
          <p className='text-xs text-success-800 leading-3'>Há 3 minutos</p>
        </div>
      </div>
    </div>
  );
};

export default TimiStatus;
