'use client';

import { Calendar, CheckCircle } from '@phosphor-icons/react';
import dayjs from 'dayjs';

const formatDuration = (endedAt: string, createdAt: string) => {
  const duration = dayjs(endedAt).diff(dayjs(createdAt), 'second');
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;

  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');

  return hours > 0
    ? `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
    : `${formattedMinutes}:${formattedSeconds}`;
};

const TimiRunItem = ({ run }: { run: any }) => {
  const duration = dayjs(run.ended_at).diff(dayjs(run.created_at), 'second');
  const averageTimePerHotel = duration / run.processed_hotels_count;

  return (
    <div className='flex flex-col bg-white rounded-default p-3'>
      <div className='flex justify-between items-center'>
        <div className='flex items-center gap-1'>
          <CheckCircle className='text-success-500' />
          <p className='text-sm font-medium'>
            Execução{' '}
            <span className='text-xs text-gray-500'>
              #{run.id.split('-')[0]}
            </span>
          </p>
        </div>
      </div>
      <div className='mt-3'>
        <div className='flex justify-between gap-6'>
          <div className='flex gap-6'>
            <div>
              <p className='text-sm text-gray-500'>Tempo:</p>
              <h3 className='text-primary-900 font-medium'>
                {formatDuration(run.ended_at, run.created_at)}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Média:</p>
              <h3 className='text-primary-900 font-medium'>
                {averageTimePerHotel.toFixed(2)}
                s/hotel
              </h3>
            </div>
          </div>
          <div className='flex gap-6'>
            <div className='text-right'>
              <p className='text-sm text-gray-500'>Hotéis</p>
              <h3 className='text-primary-900 font-medium'>
                {run.hotels_count}
              </h3>
            </div>
            <div className='text-right'>
              <p className='text-sm text-gray-500'>Processados</p>
              <h3 className='text-primary-900 font-medium'>
                {run.processed_hotels_count}
              </h3>
            </div>
            <div className='text-right'>
              <p className='text-sm text-gray-500'>Progresso</p>
              <h3 className='text-primary-900 font-medium'>
                {(
                  (run.processed_hotels_count / run.hotels_count) *
                  100
                ).toFixed(0)}
                %
              </h3>
            </div>
          </div>
        </div>
      </div>
      <div className='flex flex-col gap-2 mt-6'>
        <div className='flex justify-between'>
          <p className='text-sm text-gray-500 flex items-center gap-1'>
            <Calendar />
            Iniciada em:
          </p>
          <p className='text-sm font-medium'>
            {dayjs(run.created_at).subtract(3, 'hour').format('DD MMM HH:mm')}
          </p>
        </div>
        <div className='flex justify-between'>
          <p className='text-sm text-gray-500 flex items-center gap-1'>
            <CheckCircle />
            Finalizada em:
          </p>
          <p className='text-sm font-medium'>
            {dayjs(run.ended_at).subtract(3, 'hour').format('DD MMM HH:mm')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default TimiRunItem;
