'use client';

import { useEffect, useState, useRef } from 'react';
import dayjs from 'dayjs';

interface TimerDisplayProps {
  startTime: string;
  className?: string;
}

const TimerDisplay = ({ startTime, className = '' }: TimerDisplayProps) => {
  const [duration, setDuration] = useState<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize duration
  useEffect(() => {
    setDuration(dayjs().diff(dayjs(startTime), 'second'));
  }, [startTime]);

  // Set up interval for duration updates
  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Create new interval
    intervalRef.current = setInterval(() => {
      setDuration(dayjs().diff(dayjs(startTime), 'second'));
    }, 1000);

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [startTime]);

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;

    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const formattedSeconds = seconds.toString().padStart(2, '0');

    return hours > 0
      ? `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
      : `${formattedMinutes}:${formattedSeconds}`;
  };

  return (
    <span className={className}>
      {formatDuration(duration)}
    </span>
  );
};

export default TimerDisplay;
