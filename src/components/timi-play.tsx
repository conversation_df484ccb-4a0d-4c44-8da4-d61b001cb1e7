'use client';

import { run, trivagoServicePing } from '@/services/timi';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@ourtrip/ui';
import { FileCsv, Plus, Upload, X } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const TimiPlay = ({ trivagoStatus }: { trivagoStatus: boolean }) => {
  const router = useRouter();
  const [isSending, setIsSending] = useState<boolean>(false);
  const [headers, setHeaders] = useState<string[]>([]);

  const { register, handleSubmit, watch, reset, setValue } = useForm<{
    file: File | null;
    filters: { column: string; value: string }[];
  }>({
    defaultValues: {
      file: null,
      filters: []
    }
  });

  const file = watch('file');
  const filters = watch('filters');

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      reset({ file: files?.[0], filters: [] });
    }
  };

  useEffect(() => {
    if (file instanceof FileList) {
      reset({ file: file?.[0], filters: [] });
      return;
    }

    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        const csvText = e.target?.result as string;
        const lines = csvText.split('\n');
        if (lines.length > 0) {
          if (lines[0].includes(',')) {
            setHeaders(lines[0].split(',').map(header => header.trim()));
          }
          if (lines[0].includes(';')) {
            setHeaders(lines[0].split(';').map(header => header.trim()));
          }
        }
      };
      reader.readAsText(file as File);
    } else {
      setHeaders([]);
    }
  }, [file]);

  const handleSendInventory = async (values: {
    file: File | null;
    filters: { column: string; value: string }[];
  }) => {
    if (!values.file) return;

    setIsSending(true);
    const formData = new FormData();
    formData.append('file', values.file, values.file.name);
    formData.append('filters', JSON.stringify({ filters: values.filters }));

    try {
      const response = await run(formData);

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success('Execução iniciada com sucesso');
        reset({ file: null, filters: [] });
        setHeaders([]);
        router.refresh();
      }
    } catch (error) {
      toast.error('Erro ao enviar arquivo de inventário');
    }

    setIsSending(false);
  };

  return (
    <form
      className='flex flex-col bg-white rounded-default p-3 gap-2'
      onSubmit={handleSubmit(handleSendInventory)}
    >
      <p className='text-sm text-gray-500'>Executar</p>
      {!file && (
        <div
          className='w-full border-2 border-dashed border-gray-200 rounded-inner p-6 text-center cursor-pointer hover:border-gray-400 transition-colors'
          onDrop={handleDrop}
          onDragOver={e => e.preventDefault()}
          onClick={() => document.getElementById('inventoryFileInput')?.click()}
          role='button'
          tabIndex={0}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              document.getElementById('inventoryFileInput')?.click();
            }
          }}
          aria-label='Clique ou arraste o arquivo de inventário para enviar'
        >
          <Upload size={20} className='mx-auto text-gray-500' />
          <p className='mt-2 text-sm text-gray-500'>
            Clique ou arraste o arquivo de inventário
          </p>
        </div>
      )}
      <input
        type='file'
        id='inventoryFileInput'
        {...register('file')}
        className='hidden'
        multiple={false}
        accept='.csv'
      />
      {file && (
        <div className='w-full bg-gray-100 rounded-inner flex items-center justify-between gap-2 p-3 overflow-hidden'>
          <div className='flex items-center gap-2'>
            <FileCsv size={22} className='flex-none text-gray-500' />
            <p className='text-sm text-primary-900 line-clamp-1'>{file.name}</p>
          </div>
          <X
            size={14}
            className='text-gray-500 cursor-pointer flex-none'
            onClick={() => {
              reset({ file: null, filters: [] });
              setHeaders([]);
            }}
          />
        </div>
      )}
      {headers.length > 0 && (
        <div className='w-full flex flex-col gap-2 mb-3'>
          <p className='text-sm text-gray-500 mt-2'>Filtros</p>
          {filters?.map((filter, index) => (
            <div key={index} className='flex gap-2 items-center'>
              <Select
                {...register(`filters.${index}.column`)}
                onValueChange={value =>
                  setValue(`filters.${index}.column`, value)
                }
                defaultValue={filter.column}
              >
                <SelectTrigger className='flex-2'>
                  <SelectValue placeholder='Selecione a Coluna' />
                </SelectTrigger>
                <SelectContent>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                placeholder='Valor'
                {...register(`filters.${index}.value`)}
                color='gray'
                className='flex-4'
              />
              <Button
                color='gray'
                size='icon'
                onClick={() => {
                  const newFilters = [...filters];
                  newFilters.splice(index, 1);
                  reset({ file: file, filters: newFilters });
                }}
              >
                <X />
              </Button>
            </div>
          ))}
          <Button
            color='gray'
            size='small'
            onClick={() => {
              const newFilters = [...filters];
              newFilters.push({ column: '', value: '' });
              reset({ file: file, filters: newFilters });
            }}
            type='button'
          >
            <Plus size={14} />
          </Button>
        </div>
      )}
      <Button
        color='primary'
        disabled={!trivagoStatus || !file || !headers.length}
        loading={isSending}
        type='submit'
      >
        {!trivagoStatus ? 'Serviço indisponível' : 'Iniciar'}
      </Button>
    </form>
  );
};

export default TimiPlay;
