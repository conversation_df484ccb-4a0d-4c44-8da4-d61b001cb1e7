'use client';

import { useEffect, useState, useRef, useMemo } from 'react';
import { Badge } from '@ourtrip/ui';
import { Clock } from '@phosphor-icons/react';
import supabase from '@/services/supabase-client';
import dayjs from 'dayjs';
import { Run } from '@/lib/definitions/timi';

const TimiRunning = ({ runId }: { runId?: string }) => {
  const [run, setRun] = useState<Run | null>(null);
  const [duration, setDuration] = useState<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update duration when run changes
  useEffect(() => {
    if (!run) return;
    setDuration(dayjs().diff(dayjs(run.created_at), 'second'));
  }, [run]);

  // Set up interval for duration updates
  useEffect(() => {
    if (!run) return;

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Create new interval
    intervalRef.current = setInterval(() => {
      setDuration(dayjs().diff(dayjs(run.created_at), 'second'));
    }, 1000);

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [run]);

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;

    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const formattedSeconds = seconds.toString().padStart(2, '0');

    return hours > 0
      ? `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
      : `${formattedMinutes}:${formattedSeconds}`;
  };

  const calculateProgress = () => {
    if ((run?.hotels_count || 0) === 0) return 0;

    return (
      ((run?.processed_hotels_count || 0) / (run?.hotels_count || 0)) *
      100
    ).toFixed(0);
  };

  const calculateEstimatedTime = () => {
    if ((run?.processed_hotels_count || 0) === 0) return 0;
    const averageTimePerHotel = duration / (run?.processed_hotels_count || 0);
    const hotelsLeft =
      (run?.hotels_count || 0) - (run?.processed_hotels_count || 0);
    return Math.round(averageTimePerHotel * hotelsLeft);
  };

  useEffect(() => {
    if (!runId) return;

    const fetchRun = async () => {
      const { data } = await supabase
        .from('run')
        .select('*')
        .eq('id', runId)
        .single();

      setRun(data!);
    };

    fetchRun();
  }, [runId]);

  useEffect(() => {
    if (!runId) return;

    const realtimeRun = supabase
      .channel('schema-db-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'run',
          filter: `id=eq.${runId}`
        },
        payload => {
          setRun(payload.new as Run);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(realtimeRun);
    };
  }, [runId]);

  const progressPercentage = useMemo(
    () => calculateProgress(),
    [run?.hotels_count, run?.processed_hotels_count]
  );
  const averageTimePerHotel = useMemo(
    () => duration / (run?.processed_hotels_count || 1),
    [duration, run?.processed_hotels_count]
  );
  const estimatedTimeLeft = useMemo(
    () => calculateEstimatedTime(),
    [duration, run?.processed_hotels_count, run?.hotels_count]
  );

  return (
    run && (
      <div className='flex flex-col bg-white rounded-default p-3'>
        <div className='flex items-center justify-between gap-2'>
          <p className='text-sm font-medium flex items-center gap-1'>
            <Clock className='text-info-900' />
            Execução{' '}
            <span className='text-xs text-gray-500'>
              #{run.id.split('-')[0]}
            </span>
          </p>
          <Badge type='info' size='small'>
            Executando
          </Badge>
        </div>
        <div className='mt-3'>
          <div className='flex gap-6'>
            <div>
              <p className='text-sm text-gray-500'>Tempo:</p>
              <h3 className='text-primary-900 font-medium'>
                {formatDuration(duration)}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Média:</p>
              <h3 className='text-primary-900 font-medium'>
                {averageTimePerHotel.toFixed(2)}s/hotel
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Estimado:</p>
              <h3 className='text-primary-900 font-medium'>
                {formatDuration(estimatedTimeLeft)}
              </h3>
            </div>
          </div>
          <div className='flex justify-between items-center mb-2 mt-2'>
            <p className='text-sm text-gray-500'>Progresso</p>
            <span className='text-sm font-medium text-primary-900'>
              {progressPercentage}%
            </span>
          </div>
          <div className='w-full bg-gray-200 rounded-full h-2'>
            <div
              className='bg-info-600 h-2 rounded-full transition-all duration-300'
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className='flex justify-between items-center mt-1 text-sm text-gray-500'>
            <p>{run.processed_hotels_count}</p>
            <p>{run.hotels_count}</p>
          </div>
        </div>
        <div className='mt-3'>
          <p className='text-sm text-gray-500'>Filtros</p>
        </div>
        {/* run.filters = [{'column': 'Partner Country ID', 'value': 'BR'}] */}
      </div>
    )
  );
};

export default TimiRunning;
