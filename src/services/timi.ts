'use server';

const TIMI_SERVICE_URL = process.env.NEXT_PUBLIC_TIMI_SERVICE_URL;
const TRIVAGO_SERVICE_URL = process.env.NEXT_PUBLIC_TRIVAGO_SERVICE_URL;

export const trivagoServicePing = async (): Promise<{
  data: boolean;
}> => {
  try {
    const response = await fetch(`${TRIVAGO_SERVICE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      return {
        data: false
      };
    }

    return {
      data: response.ok
    };
  } catch (error) {
    return {
      data: false
    };
  }
};

export const timiServicePing = async (): Promise<{
  data?: boolean;
  error?: string;
}> => {
  const response = await fetch(`${TIMI_SERVICE_URL}/health`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    return {
      error: 'Falha ao buscar ping'
    };
  }

  return {
    data: response.ok
  };
};

export const run = async (
  formData: FormData
): Promise<{
  data?: boolean;
  error?: string;
}> => {
  const response = await fetch(`${TIMI_SERVICE_URL}/run`, {
    method: 'POST',
    body: formData
  });

  console.log(await response.json());

  if (!response.ok) {
    return {
      error: 'Falha ao executar'
    };
  }

  return {
    data: response.ok
  };
};
